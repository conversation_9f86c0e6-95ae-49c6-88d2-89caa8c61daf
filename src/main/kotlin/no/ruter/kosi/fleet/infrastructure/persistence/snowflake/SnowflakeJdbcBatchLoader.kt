package no.ruter.kosi.fleet.infrastructure.persistence.snowflake

import com.fasterxml.jackson.databind.ObjectMapper
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQualityEntity
import no.ruter.kosi.fleet.domain.quality.VehicleQualityEntity
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntityMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.stereotype.Service
import java.sql.Timestamp
import java.time.Instant

@Service
@ConditionalOnBean(JdbcTemplate::class)
class SnowflakeJdbcBatchLoader(
    @Qualifier("snowflakeJdbcTemplate") private val jdbcTemplate: JdbcTemplate,
    private val vehicleEntityMapper: VehicleEntityMapper,
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(SnowflakeJdbcBatchLoader::class.java)

    private val insertContractsSql = """
        INSERT INTO CONTRACTS (
          contract_id, contract_id_in_frida, name
        ) VALUES (?,?,?)
    """.trimIndent()

    private val insertFieldQualitySql = """
        INSERT INTO VEHICLE_FIELD_QUALITY (
          id, vehicle_quality_id, field_name, field_value,
          quality_type, message, created_at
        ) VALUES (?,?,?,?,?,?,?,?)
    """.trimIndent()

    private val insertQualitySql = """
        INSERT INTO VEHICLE_QUALITY (
          id, vehicle_id, business_key,
          effective_from, effective_to, is_current
        ) VALUES (?,?,?,?,?,?)
    """.trimIndent()

    private val insertVehiclesSql = """
        INSERT INTO VEHICLES (
          vehicle_id, internal_vehicle_id, vehicle_ref,
          identification, registration, features,
          capacity, contractor, technical,
          effective_from, effective_to, is_current,
          created_at, updated_at
        )
        SELECT 
          ?, ?, ?,
          OBJECT_CONSTRUCT(?), 
          OBJECT_CONSTRUCT(?),
          OBJECT_CONSTRUCT(?),
          OBJECT_CONSTRUCT(?),
          OBJECT_CONSTRUCT(?),
          OBJECT_CONSTRUCT(?),
          ?, ?, ?, ?, ?
    """.trimIndent()

    fun batchInsertContracts(vehicleEntities: List<VehicleEntity>) {
        logger.info("Starting bulk insert of contracts")
        val startTime = System.currentTimeMillis()

        try {
            val args = vehicleEntities.map { it.contractor }
                .distinctBy { it?.id }
                .filterNotNull()
                .map { c -> arrayOf<Any?>(c.id, c.contractIdInFrida, c.name) }

            if (args.isEmpty()) {
                logger.info("No contracts to insert")
                return
            }

            val results = jdbcTemplate.batchUpdate(insertContractsSql, args)
            val elapsedTime = System.currentTimeMillis() - startTime
            logger.info("Completed bulk insert of ${results.sum()} contracts in ${elapsedTime}ms")
        } catch (e: Exception) {
            throw RuntimeException("Contract batch insert failed", e)
        }
    }

    fun batchInsertFieldQualities(fieldQualities: List<VehicleFieldQualityEntity>) {
        logger.info("Starting bulk insert of ${fieldQualities.size} field qualities")
        val startTime = System.currentTimeMillis()

        try {
            val args = fieldQualities.distinctBy { it.id }
                .map { fq -> arrayOf<Any?>(
                    fq.id,
                    fq.vehicleQuality.id,
                    fq.fieldName,
                    fq.fieldValue,
                    fq.qualityType,
                    fq.message,
                    Timestamp.from(fq.createdAt)
                ) }

            if (args.isEmpty()) {
                logger.info("No field qualities to insert")
                return
            }

            val maxBatchSize = 2000  //
            args.chunked(maxBatchSize).forEachIndexed { batchIndex, batch ->
                val batchStartTime = System.currentTimeMillis()
                val results = jdbcTemplate.batchUpdate(insertFieldQualitySql, batch)
                val batchElapsedTime = System.currentTimeMillis() - batchStartTime
                logger.info("Completed batch ${batchIndex + 1}/${(args.size + maxBatchSize - 1) / maxBatchSize} in ${batchElapsedTime}ms. Inserted ${results.sum()} field qualities.")
            }

            val elapsedTime = System.currentTimeMillis() - startTime
            logger.info("Completed bulk insert of all field qualities in ${elapsedTime}ms")
        } catch (e: Exception) {
            throw RuntimeException("Field qualities batch insert failed", e)
        }
    }

    fun batchInsertVehicleQualities(qualities: List<VehicleQualityEntity>) {
        logger.info("Starting bulk insert of ${qualities.size} vehicle qualities")
        val startTime = System.currentTimeMillis()

        try {
            val args = qualities.distinctBy { it.id }
                .map { q -> arrayOf<Any?>(
                    q.id,
                    q.vehicleId,
                    q.businessKey,
                    Timestamp.from(q.effectiveFrom),
                    q.effectiveTo?.let { Timestamp.from(it) },
                    q.isCurrent
                ) }

            if (args.isEmpty()) {
                logger.info("No vehicle qualities to insert")
                return
            }

            val maxBatchSize = 2000
            args.chunked(maxBatchSize).forEachIndexed { batchIndex, batch ->
                val batchStartTime = System.currentTimeMillis()
                val results = jdbcTemplate.batchUpdate(insertQualitySql, batch)
                val batchElapsedTime = System.currentTimeMillis() - batchStartTime
                logger.info("Completed batch ${batchIndex + 1}/${(args.size + maxBatchSize - 1) / maxBatchSize} in ${batchElapsedTime}ms. Inserted ${results.sum()} vehicle qualities.")
            }

            val elapsedTime = System.currentTimeMillis() - startTime
            logger.info("Completed bulk insert of all vehicle qualities in ${elapsedTime}ms")
        } catch (e: Exception) {
            throw RuntimeException("Vehicle qualities batch insert failed", e)
        }
    }

    fun batchInsertVehicles(vehicleEntities: List<VehicleEntity>) {
        logger.info("Starting optimized bulk insert of ${vehicleEntities.size} vehicles")

        val maxBatchSize = 2000
        val now = Instant.now()

        // serialize once
        logger.info("Preparing data for ${vehicleEntities.size} vehicles...")
        val prepStartTime = System.currentTimeMillis()

        val preparedData = vehicleEntities.map { veh ->
            Triple(veh, veh, now)
        }

        val prepTime = System.currentTimeMillis() - prepStartTime
        logger.info("Data preparation completed in ${prepTime}ms")

        preparedData.chunked(maxBatchSize).forEachIndexed { batchIndex, batch ->
            logger.info("Processing batch ${batchIndex + 1}/${(preparedData.size + maxBatchSize - 1) / maxBatchSize} with ${batch.size} vehicles")

            val startTime = System.currentTimeMillis()

            val validBatch = batch.filter { (veh, _, _) ->
                val isValid = veh.id != null && veh.vehicleRef.isNotBlank()
                if (!isValid) {
                    logger.warn("Skipping invalid vehicle: id=${veh.id}, ref=${veh.vehicleRef}")
                }
                isValid
            }

            if (validBatch.isEmpty()) {
                logger.warn("No valid vehicles in batch ${batchIndex + 1}, skipping")
                return@forEachIndexed
            }

            val batchArgs = validBatch.map { (veh, _, timestamp) ->
                arrayOf<Any?>(
                    veh.id,
                    veh.internalVehicleId,
                    veh.vehicleRef,
                    objectMapper.writeValueAsString(veh.identification),
                    objectMapper.writeValueAsString(veh.registration),
                    objectMapper.writeValueAsString(veh.features),
                    veh.capacity?.let { objectMapper.writeValueAsString(it) } ?: "{}",
                    veh.contractor?.let { objectMapper.writeValueAsString(it) } ?: "{}",
                    objectMapper.writeValueAsString(veh.technical),
                    Timestamp.from(veh.effectiveFrom),
                    veh.effectiveTo?.let { Timestamp.from(it) },
                    veh.isCurrent,
                    Timestamp.from(timestamp),
                    null
                )
            }

            val sql = """
                INSERT INTO VEHICLES (
                  vehicle_id, internal_vehicle_id, vehicle_ref,
                  identification, registration, features,
                  capacity, contractor, technical,
                  effective_from, effective_to, is_current,
                  created_at, updated_at
                )
                SELECT
                  ?, ?, ?,
                  TO_VARIANT(?),
                  TO_VARIANT(?),
                  TO_VARIANT(?),
                  TO_VARIANT(?),
                  TO_VARIANT(?),
                  TO_VARIANT(?),
                  ?, ?, ?, ?, ?
            """.trimIndent()

            try {
                val valuesClause = batchArgs.joinToString(",") { args ->
                    val escapedArgs = args.map { arg ->
                        when (arg) {
                            is String -> "'${arg.replace("'", "''")}'"
                            is Timestamp -> "'${arg}'"
                            null -> "NULL"
                            else -> arg.toString()
                        }
                    }
                    "(${escapedArgs.joinToString(",")})"
                }

                val bulkSql = """
                    INSERT INTO VEHICLES (
                      vehicle_id, internal_vehicle_id, vehicle_ref,
                      identification, registration, features,
                      capacity, contractor, technical,
                      effective_from, effective_to, is_current,
                      created_at, updated_at
                    )
                    SELECT
                      column1, column2, column3,
                      TO_VARIANT(column4),
                      TO_VARIANT(column5),
                      TO_VARIANT(column6),
                      TO_VARIANT(column7),
                      TO_VARIANT(column8),
                      TO_VARIANT(column9),
                      column10, column11, column12, column13, column14
                    FROM VALUES $valuesClause
                """.trimIndent()

                val insertedCount = jdbcTemplate.update(bulkSql)
                val elapsedTime = System.currentTimeMillis() - startTime
                val vehiclesPerSecond = (insertedCount.toDouble() / (elapsedTime / 1000.0)).toInt()

                logger.info("Batch ${batchIndex + 1} completed in ${elapsedTime}ms. " +
                           "Inserted $insertedCount vehicles (${vehiclesPerSecond} vehicles/second)")

            } catch (e: Exception) {
                throw RuntimeException("Batch insert failed - no fallback to individual inserts", e)
            }
        }

        logger.info("Completed bulk insert of all vehicles")
    }

    fun clearAll() {
        jdbcTemplate.execute("TRUNCATE TABLE VEHICLE_FIELD_QUALITY")
        jdbcTemplate.execute("TRUNCATE TABLE VEHICLE_QUALITY")
        jdbcTemplate.execute("TRUNCATE TABLE CONTRACTS")
        jdbcTemplate.execute("TRUNCATE TABLE VEHICLES")
    }
}
