package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.scd2.SCD2Repository
import no.ruter.kosi.fleet.domain.scd2.SCD2RepositoryBase
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository
interface SpringDataVehicleRepository : SCD2RepositoryBase<VehicleEntity, Int, String> {
    fun findTopByIdAndIsCurrentIsTrue(vehicleId: Int): VehicleEntity?

    fun findLatestByVehicleRefAndIsCurrentIsTrue(@Param("vehicleRef") vehicleRef: String): VehicleEntity?

    @Query(
        """
        SELECT v FROM VehicleEntity v
        LEFT JOIN FETCH v.vehicleQuality q
        LEFT JOIN FETCH q.fieldQualities fq
        WHERE v.isCurrent = true
    """
    )
    fun findAllWithRelationships(): List<VehicleEntity>

    @Query(
        """
        SELECT v FROM VehicleEntity v 
        LEFT JOIN FETCH v.vehicleQuality q
        LEFT JOIN FETCH q.fieldQualities fq
        WHERE v.id IN :ids
    """
    )
    fun findAllWithRelationshipsByIds(@Param("ids") ids: List<Int>): List<VehicleEntity>

    @Query(
        """
        SELECT v.id FROM VehicleEntity v
        WHERE v.isCurrent = true
    """
    )
    fun findAllICurrentIds(): List<Int>

    @Query(
        """
        SELECT v FROM VehicleEntity v 
        WHERE v.vehicleRef = :vehicleRef
    """
    )
    fun findByVehicleRefWithRelationships(vehicleRef: String): VehicleEntity?

    fun findAllByIsCurrentIsTrue(): List<VehicleEntity>

    @Modifying
    @Query("DELETE FROM VehicleEntity")
    fun deleteAllDirectly()
}
