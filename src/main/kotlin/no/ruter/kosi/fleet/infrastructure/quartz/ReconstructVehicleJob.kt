package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.domain.vehicle.VehicleService
import no.ruter.kosi.fleet.infrastructure.cache.CacheService
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.SnowflakePublicationService
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class ReconstructVehicleJob(
    private val vehicleService: VehicleService,
    private val publicationService: SnowflakePublicationService,
    override val jobService: JobService,
    private val cacheService: CacheService
) : BaseJob() {
    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    ) {
        MdcUtils.setVehicleRef("all")

        try {
            logger.info("Starting vehicle publication to Snowflake")

            // clear all vehicle caches
            cacheService.evictCache("vehiclesList")
            cacheService.evictCache("vehicleDetails")
            cacheService.evictCache("vehicleReconstruction")

            // Get current vehicle count for progress tracking
            val currentVehicles = vehicleService.getAllSnapshotsWithRelationships()
            val totalCount = currentVehicles.size

            fleetJob?.let {
                it.totalItems = totalCount
                jobService.save(it)
            }

            logger.info("Found {} current vehicles to publish to Snowflake", totalCount)

            publicationService.publishVehiclesToSnowflake(context)

            logger.info("Completed vehicle publication to Snowflake for {} vehicles", totalCount)

        } catch (e: Exception) {
            logger.error("Error during vehicle publication: {}", e.message, e)
            fleetJob?.let {
                jobService.recordJobError(it.jobId, "Error during vehicle publication: ${e.message}")
            }
            throw e
        }
    }
}
