package no.ruter.kosi.fleet.infrastructure.client.frida

import com.fasterxml.jackson.databind.ObjectMapper
import no.ruter.kosi.fleet.domain.event.EventService
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.vehicle.FleetVehicleRefGenerator
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fridaclient.models.ApiVehicleV2Model
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import kotlin.reflect.full.memberProperties

@Service
class FridaVehicleImporter
@Autowired
constructor(
    private val eventService: EventService,
    private val objectMapper: ObjectMapper,
    private val vehicleRepository: AuroraInternalVehicleRepository,
) {
    private val logger = LoggerFactory.getLogger(FridaVehicleImporter::class.java)

    fun processVehicles(
        fridaVehicles: List<ApiVehicleV2Model>,
        job: FleetJob,
        onVehicleProcessed: ((count: Int) -> Unit)? = null,
        isCancelled: (() -> Boolean)? = null,
    ) {
        MdcUtils.setJobId(job.jobId)

        logger.info("Processing {} vehicles from FRIDA", fridaVehicles.size)

        var processedCount = 0
        for ((index, externalVehicle) in fridaVehicles.withIndex()) {
            if (isCancelled?.invoke() == true) {
                logger.info("Job cancelled, stopping vehicle processing after {} of {} vehicles", 
                    processedCount, fridaVehicles.size)
                break
            }

            MdcUtils.setJobId(job.jobId)
            MdcUtils.setVehicleId(externalVehicle.id)
            MdcUtils.setVehicleRef(externalVehicle.chassiNumber)
            try {
                logger.debug("Processing FRIDA vehicle with ID: {}", externalVehicle.id)
                processVehicle(externalVehicle, job)
                logger.debug("Successfully processed FRIDA vehicle with ID: {}", externalVehicle.id)
                processedCount++
            } catch (e: Exception) {
                logger.error(
                    "Error importing vehicle from FRIDA with ID {}: {}",
                    externalVehicle.id,
                    e.message,
                    e,
                )

                if (logger.isTraceEnabled) {
                    try {
                        logger.trace(
                            "Vehicle data:\n--------------------\n{}\n--------------------------------",
                            objectMapper.writeValueAsString(externalVehicle),
                        )
                    } catch (ex: Exception) {
                        // ignore serialization errors in logging
                    }
                }
            } finally {
                onVehicleProcessed?.invoke(index + 1)
            }
        }

        logger.info("Completed processing of {} FRIDA vehicles", processedCount)
    }

    @Transactional
    fun processVehicle(
        externalVehicle: ApiVehicleV2Model,
        job: FleetJob,
    ) {
        val extractedFields = extractFields(externalVehicle)

        // TODO I said it before, but we need a tet (or rather nation or european) wide vehicle reference
        // for now we use frida's id, which works until we add vehicles that aren't in frida
        val (vehicleRef, quality) = FleetVehicleRefGenerator.generateFleetVehicleRef(
            fridaId = externalVehicle.id
        )

        if (quality.type == QualityType.ENRICHED_VALUE) {
            logger.info("Generated vehicle reference for vehicle ${externalVehicle.id}: $vehicleRef")
        }

        // we need to remove whitespaces, because frida likes to add them
        var publicIdString: String? = extractedFields["publicIDString"].toString().replace(" ", "")
        if (publicIdString == "null") {
            // just to be sure
            publicIdString = null
        }
        // fall back to temporaryPublicIDString
        val temporaryPublicIdString = extractedFields["temporaryPublicIDString"].toString()
        if (publicIdString == null && temporaryPublicIdString.isNotEmpty()) {
            publicIdString = temporaryPublicIdString
        }

        val managedImport = job

        val vehicle =
            vehicleRepository.findBySourceId(externalVehicle.id) ?: run {
                logger.info(
                    "Creating new vehicle for FRIDA ID: {}, vehicleRef: {}",
                    externalVehicle.id,
                    vehicleRef,
                )

                val newVehicle =
                    InternalVehicle(
                        dataSource = DataSource.FRIDA,
                        sourceId = externalVehicle.id,
                        publicIdString = publicIdString,
                        vehicleRef = vehicleRef,
                    )
                vehicleRepository.save(newVehicle)
            }

        MdcUtils.setVehicleId(vehicle.vehicleId)
        MdcUtils.setVehicleRef(vehicle.vehicleRef)

        // Check for missing data
        if (vehicleRef == null && publicIdString == null) {
            logger.warn("Vehicle {} is missing both vehicleRef and publicIdString", vehicle.vehicleId)
        } else if (vehicleRef == null) {
            logger.warn("Vehicle {} is missing vehicleRef", vehicle.vehicleId)
        }

        eventService.storeChangedFieldsFromVehicle(
            vehicle,
            extractedFields,
            DataSource.FRIDA,
            managedImport,
        )
    }

    private fun extractFields(externalVehicle: ApiVehicleV2Model): Map<String, Any?> {
        val fieldsMap = mutableMapOf<String, Any?>()
        val vehicleClass = externalVehicle::class
        for (property in vehicleClass.memberProperties) {
            val fieldName = property.name
            if (fieldName == "customAttributeValues") {
                // handled later
                continue
            }
            try {
                val fieldValue = property.getter.call(externalVehicle)
                fieldsMap[fieldName] = fieldValue
            } catch (e: Exception) {
                logger.error("Error extracting field '${property.name}': ${e.message}", e)
            }
        }

        // process custom attribute values separately
        val customAttributes = externalVehicle.customAttributeValues ?: emptyList()
        for (customAttr in customAttributes) {
            try {
                val attributeName =
                    customAttr.vehicleAttributeName
                        ?: throw IllegalStateException("Can't store attribute without an attribute name")
                val attributeValue = customAttr.value
                val safeAttributeName = attributeName.replace("\\s+".toRegex(), "_")
                fieldsMap["customAttr_$safeAttributeName"] = attributeValue
            } catch (e: Exception) {
                logger.error("Error extracting custom attribute: ${e.message}", e)
            }
        }
        return fieldsMap
    }
}
