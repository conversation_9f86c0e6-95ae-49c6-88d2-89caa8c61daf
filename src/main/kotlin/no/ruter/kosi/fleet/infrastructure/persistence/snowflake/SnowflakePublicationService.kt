package no.ruter.kosi.fleet.infrastructure.persistence.snowflake

import no.ruter.kosi.fleet.domain.vehicle.VehicleService
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class SnowflakePublicationService(
    private val vehicleService: VehicleService,
    private val batchLoader: SnowflakeJdbcBatchLoader,
    private val jobService: JobService
) {
    private val logger = LoggerFactory.getLogger(SnowflakePublicationService::class.java)

    @Transactional
    fun publishVehiclesToSnowflake(context: JobExecutionContext) {
        val totalStartTime = System.currentTimeMillis()
        logger.info("Starting Snowflake publication")

        val jobDataMap = context.mergedJobDataMap
        val jobId: Int? = if (jobDataMap.containsKey("jobId")) jobDataMap.getInt("jobId") else null
        val fleetJob: FleetJob? = jobId?.let { jobService.findJob(it) }

        logger.info("Loading current vehicles with relationships from database")
        val loadStartTime = System.currentTimeMillis()
        val vehicles = vehicleService.getAllSnapshotsWithRelationships()
        val total = vehicles.size
        val loadTime = System.currentTimeMillis() - loadStartTime
        logger.info("Loaded $total current vehicles in ${loadTime}ms (${total.toDouble() / (loadTime / 1000.0)} vehicles/second)")

        if (fleetJob != null) {
            fleetJob.totalItems = total
            jobService.save(fleetJob)
        }

        try {
            logger.info("Clearing existing data in Snowflake")
            val clearStartTime = System.currentTimeMillis()
            batchLoader.clearAll()
            val clearTime = System.currentTimeMillis() - clearStartTime
            logger.info("Cleared all data in ${clearTime}ms")

            logger.info("Copying contract data")
            val contractStartTime = System.currentTimeMillis()
            batchLoader.batchInsertContracts(vehicles)
            val contractTime = System.currentTimeMillis() - contractStartTime
            logger.info("Contract insertion completed in ${contractTime}ms")

            logger.info("Preparing field quality data")
            val qualityStartTime = System.currentTimeMillis()
            val allFieldQualities = vehicles.mapNotNull { it.vehicleQuality }.flatMap { it.fieldQualities }
            logger.info("Extracted ${allFieldQualities.size} field qualities")
            batchLoader.batchInsertFieldQualities(allFieldQualities)
            val qualityTime = System.currentTimeMillis() - qualityStartTime
            logger.info("Field quality insertion completed in ${qualityTime}ms")

            logger.info("Preparing vehicle quality data")
            val vehQualityStartTime = System.currentTimeMillis()
            val allQualities = vehicles.mapNotNull { it.vehicleQuality }
            logger.info("Extracted ${allQualities.size} vehicle qualities")
            batchLoader.batchInsertVehicleQualities(allQualities)
            val vehQualityTime = System.currentTimeMillis() - vehQualityStartTime
            logger.info("Vehicle quality insertion completed in ${vehQualityTime}ms")

            logger.info("Preparing vehicle data for bulk insert")
            val vehicleStartTime = System.currentTimeMillis()
            batchLoader.batchInsertVehicles(vehicles)
            val vehicleTime = System.currentTimeMillis() - vehicleStartTime
            logger.info("Vehicle insertion completed in ${vehicleTime}ms")

            fleetJob?.let { jobService.updateJobProgress(it.jobId, total, total) }

            val totalTime = System.currentTimeMillis() - totalStartTime
            val vehiclesPerSecond = total.toDouble() / (totalTime / 1000.0)
            logger.info("Snowflake publication completed in ${totalTime}ms (${vehiclesPerSecond.toInt()} vehicles/second)")
            logger.info("Performance breakdown: Load=${loadTime}ms, Clear=${clearTime}ms, " +
                       "Contracts=${contractTime}ms, FieldQualities=${qualityTime}ms, " +
                       "VehicleQualities=${vehQualityTime}ms, Vehicles=${vehicleTime}ms")

        } catch (ex: Exception) {
            logger.error("Error publishing to Snowflake: ${ex.message}", ex)
            fleetJob?.let { jobService.recordJobError(it.jobId, ex.message ?: "") }

            val totalTime = System.currentTimeMillis() - totalStartTime
            logger.info("Snowflake publication failed after ${totalTime}ms")
        }
    }
}
