package no.ruter.kosi.fleet.infrastructure.quartz

import arrow.core.Either
import arrow.core.right
import jakarta.persistence.EntityManager
import jakarta.persistence.EntityNotFoundException
import no.ruter.kosi.fleet.application.services.VehicleCapacityService
import no.ruter.kosi.fleet.application.services.VehicleService
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.domain.vehicle.entities.Vehicle
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleCapacityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeContractRepository
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Component
@DisallowConcurrentExecution
class PublishVehiclesToSnowflakeJob(
    private val vehicleService: VehicleService,
    private val snowflakeVehicleRepository: SnowflakeVehicleRepository,
    private val snowflakeVehicleCapacityRepository: SnowflakeVehicleCapacityRepository,
    private val snowflakeVehicleQualityRepository: SnowflakeVehicleQualityRepository,
    private val snowflakeContractRepository: SnowflakeContractRepository,
    override val jobService: JobService,
) : BaseJob() {

    @Transactional
    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    ) {
        MdcUtils.setJobName("PublishVehiclesToSnowflakeJob")
        try {
            val allVehicles = vehicleService.getAllSnapshots()
            val totalVehicles = allVehicles.size
            fleetJob?.let {
                it.totalItems = totalVehicles
                jobService.save(it)
            }
            allVehicles.forEachIndexed { index, vehicle ->
                if (fleetJob != null && jobService.findJob(fleetJob.jobId)?.status == FleetJob.JobStatus.CANCELLED) {
                    logger.info("Job cancelled, aborting PublishVehiclesToSnowflakeJob")
                    throw JobCancelledException()
                }
                try {
                    vehicle.capacity?.let { snowflakeVehicleCapacityRepository.save(it) }
                    vehicle.vehicleQuality?.let { snowflakeVehicleQualityRepository.save(it) }
                    vehicle.contract?.let { snowflakeContractRepository.save(it) }
                    val newVehicle = vehicle.copy()
                    snowflakeVehicleRepository.save(newVehicle)
                } catch (e: EntityNotFoundException) {
                    logger.warn("Vehicle not found for Snowflake publishing: ${e.message}")
                } catch (e: Exception) {
                    logger.error("Error publishing vehicle to Snowflake: ${e.message}", e)
                }
                fleetJob?.let {
                    jobService.updateJobProgress(it.jobId, index + 1, totalVehicles)
                }
            }
        } catch (e: Exception) {
            logger.error("Error during Snowflake vehicle publishing: {}", e.message, e)
            fleetJob?.let {
                jobService.recordJobError(it.jobId, "Error during Snowflake vehicle publishing: ${e.message}")
            }
            throw e
        }
    }
}
