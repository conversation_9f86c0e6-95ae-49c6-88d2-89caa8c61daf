package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiError
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaVehicleImporter
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit

@Component
@DisallowConcurrentExecution
class FridaHistoricVehicleImportJob(
    override val jobService: JobService,
    private val fridaVehicleImporter: FridaVehicleImporter,
    private val fridaApiService: FridaApiService,
) : BaseJob() {
    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    ) {
        val isCancelled = {
            fleetJob?.status == FleetJob.JobStatus.CANCELLED
        }

        try {
            val startDate = LocalDate.of(2018, 5, 3)
            val endDate = LocalDate.now()
            val totalDays = ChronoUnit.DAYS.between(startDate, endDate).toInt() + 1
            
            logger.info("Starting FRIDA import for all vehicles from {} to {} ({} days)", startDate, endDate, totalDays)

            var totalProcessedVehicles = 0
            var processedDays = 0
            
            startDate.datesUntil(endDate.plusDays(1)).use { dateStream ->
                dateStream.forEach { currentDate ->
                    try {
                        logger.info("Processing vehicles for date: {}", currentDate)
                        
                        fridaApiService.fetchVehicles(currentDate.atStartOfDay().atOffset(ZoneOffset.UTC)).fold(
                            { error ->
                                handleFridaApiError(error, fleetJob)
                                logger.warn("Failed to process date: {}", currentDate)
                            },
                            { vehicles ->
                                logger.info("Fetched {} vehicles from FRIDA for date: {}", vehicles.size, currentDate)
                                
                                val tempJob = fleetJob ?: createTempFleetJob()
                                
                                fridaVehicleImporter.processVehicles(
                                    fridaVehicles = vehicles,
                                    job = tempJob,
                                    onVehicleProcessed = { count ->
                                        fleetJob?.let {
                                            jobService.updateJobProgress(it.jobId, count, vehicles.size)
                                        }
                                    },
                                    isCancelled = isCancelled
                                )

                                totalProcessedVehicles += vehicles.size
                                processedDays++
                                
                                fleetJob?.let {
                                    val progress = (processedDays * 100) / totalDays
                                    jobService.updateJobProgress(it.jobId, progress, 100)
                                }
                                
                                logger.info("Completed processing {} vehicles for date: {}", vehicles.size, currentDate)
                            }
                        )
                    } catch (e: Exception) {
                        logger.error("Error processing date {}: {}", currentDate, e.message, e)
                        fleetJob?.let {
                            jobService.recordJobError(it.jobId, "Error processing date $currentDate: ${e.message}")
                        }
                    }
                }
            }
            
            logger.info("FRIDA import completed successfully. Processed {} vehicles across {} days.", totalProcessedVehicles, processedDays)
            
        } catch (e: Exception) {
            logger.error("Unexpected error during FRIDA import: {}", e.message, e)
            fleetJob?.let {
                jobService.recordJobError(it.jobId, "Unexpected error during FRIDA import: ${e.message}")
            }
            throw e
        }
    }

    private fun handleFridaApiError(
        error: FridaApiError,
        fleetJob: FleetJob?,
    ) {
        val errorMessage =
            when (error) {
                is FridaApiError.ClientError ->
                    "FRIDA API client error (Status code: ${error.statusCode}): ${error.message}"
                is FridaApiError.NetworkError ->
                    "FRIDA API network error: ${error.cause.message}"
                is FridaApiError.ParseError ->
                    "Error parsing FRIDA API response: ${error.cause.message}"
                is FridaApiError.EmptyResponse ->
                    "Empty response from FRIDA API: ${error.message}"
                is FridaApiError.UnexpectedError ->
                    "Unexpected error from FRIDA API: ${error.cause.message}"

                FridaApiError.Cancelled -> "FRIDA API operation was cancelled"
            }

        logger.error(errorMessage)
        fleetJob?.let {
            jobService.recordJobError(it.jobId, errorMessage)
        }
    }

    private fun createTempFleetJob(): FleetJob =
        FleetJob(
            type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
            manual = true,
            quartzJobId = "temp-frida-job-${System.currentTimeMillis()}",
            quartzJobGroup = "temp",
        )
}
