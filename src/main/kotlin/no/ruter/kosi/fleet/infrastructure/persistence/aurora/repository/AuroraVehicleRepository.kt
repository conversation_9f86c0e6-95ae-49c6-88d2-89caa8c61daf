package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.PassengerCapacity
import no.ruter.kosi.fleet.domain.vehicle.dto.CapacityDto
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.infrastructure.logging.KotlinLogger
import no.ruter.kosi.fleet.infrastructure.logging.LogCodes
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository("auroraVehicleRepository")
class AuroraVehicleRepository(
    private val jpaRepository: SpringDataVehicleRepository,
) {
    private val logger = KotlinLogger(LoggerFactory.getLogger(AuroraVehicleRepository::class.java))

    fun save(snapshot: VehicleEntity): VehicleEntity = jpaRepository.save(snapshot)

    fun findCurrentById(vehicleId: Int): VehicleEntity? = jpaRepository.findTopByIdAndIsCurrentIsTrue(vehicleId)

    fun findCurrentByVehicleRef(vehicleRef: String): VehicleEntity? =
        jpaRepository.findLatestByVehicleRefAndIsCurrentIsTrue(vehicleRef)

    fun findAllWithRelationships(): List<VehicleEntity> = jpaRepository.findAllWithRelationships()

    fun findByVehicleRefWithRelationShips(vehicleRef: String): VehicleEntity? =
        jpaRepository.findByVehicleRefWithRelationships(vehicleRef)

    fun findAll(): List<VehicleEntity> = jpaRepository.findAll()

    fun findAllCapacities(): List<CapacityDto> = jpaRepository.findAll().mapNotNull { it.capacity }

    fun findAllCurrent(): List<VehicleEntity> = jpaRepository.findAllByIsCurrentIsTrue()

    @Transactional
    fun deleteAll() {
        jpaRepository.deleteAll()
    }

    fun delete(vehicleEntity: VehicleEntity) {
        jpaRepository.delete(vehicleEntity)
    }

    fun flush() {
        jpaRepository.flush()
    }
}
