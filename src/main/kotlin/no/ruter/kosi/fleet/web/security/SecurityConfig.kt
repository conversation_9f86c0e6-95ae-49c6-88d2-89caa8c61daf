package no.ruter.kosi.fleet.web.security

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.invoke
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator
import org.springframework.security.oauth2.core.OAuth2TokenValidator
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.security.oauth2.jwt.JwtValidators
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder
import org.springframework.security.web.SecurityFilterChain
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.UrlBasedCorsConfigurationSource

@Configuration
class SecurityConfig {
    private val logger = LoggerFactory.getLogger(SecurityConfig::class.java)

    @Value("\${auth0.audience}")
    private val audience: String = String()

    @Value("\${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private val issuer: String = String()

    @Value("\${spring.security.oauth2.resourceserver.jwt.jwk-set-uri:#{null}}")
    private val jwkSetUri: String? = null

    @Bean
    fun jwtDecoder(): JwtDecoder {
        logger.info("Initializing JWT decoder with issuer: {}, audience: {}", issuer, audience)
        try {
            val jwkSetUriToUse = jwkSetUri ?: "$issuer.well-known/jwks.json"
            logger.debug("Using JWK Set URI: {}", jwkSetUriToUse)

            val jwtDecoder = NimbusJwtDecoder.withJwkSetUri(jwkSetUriToUse).build()

            val audienceValidator: OAuth2TokenValidator<Jwt> = AudienceValidator(audience)
            val withIssuer: OAuth2TokenValidator<Jwt> = JwtValidators.createDefaultWithIssuer(issuer)
            val withAudience: OAuth2TokenValidator<Jwt> = DelegatingOAuth2TokenValidator(withIssuer, audienceValidator)
            jwtDecoder.setJwtValidator(withAudience)

            logger.info("JWT decoder initialized successfully")
            return jwtDecoder
        } catch (e: Exception) {
            logger.error("Error initializing JWT decoder: {}", e.message, e)
            throw e
        }
    }

    @Bean
    fun filterChain(http: HttpSecurity): SecurityFilterChain {
        logger.debug("Configuring security filter chain")

        http.invoke {
            cors { }
            csrf { disable() }

            authorizeHttpRequests {
                logger.debug("Configuring authorization rules")
                authorize(HttpMethod.GET, "/actuator/health", permitAll)
                authorize(HttpMethod.GET, "/actuator/info", permitAll)

                authorize("/actuator/**", authenticated)
                authorize("/cache/**", authenticated)

                authorize("/vehicles/**", authenticated)
                authorize("/eventdata/**", authenticated)
                authorize("/contracts/**", authenticated)
                authorize("/jobs/**", authenticated)
            }

            oauth2ResourceServer {
                jwt {}
            }
        }

        logger.debug("Security filter chain configured successfully")
        return http.build()
    }

    @Bean
    fun corsConfigurationSource(): UrlBasedCorsConfigurationSource {
        logger.debug("Configuring CORS settings")

        val corsConfig =
            CorsConfiguration().apply {
                allowedOrigins = listOf("http://localhost:3000", "http://localhost:8080")
                allowedMethods = listOf("GET", "POST", "PUT", "DELETE", "OPTIONS")
                allowedHeaders = listOf("Authorization", "Content-Type", "X-Requested-With")
                allowCredentials = true
                maxAge = 3600L
            }

        val source = UrlBasedCorsConfigurationSource()
        source.registerCorsConfiguration("/**", corsConfig)

        logger.debug("CORS configured with origins: {}, methods: {}", corsConfig.allowedOrigins, corsConfig.allowedMethods)
        return source
    }
}
