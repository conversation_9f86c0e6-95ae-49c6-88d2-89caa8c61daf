package no.ruter.kosi.fleet.domain.job

import jakarta.annotation.PostConstruct
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.quartz.CompleteImportJob
import no.ruter.kosi.fleet.infrastructure.quartz.FridaHistoricVehicleImportJob
import no.ruter.kosi.fleet.infrastructure.quartz.ImportSchedulerProperties
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDataMap
import org.quartz.JobKey
import org.quartz.Scheduler
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.util.Locale
import java.util.UUID

@Component
class JobSchedulerManager(
    private val jobService: JobService,
    private val scheduler: Scheduler,
    private val importSchedulerProperties: ImportSchedulerProperties,
) {
    private val logger = LoggerFactory.getLogger(JobSchedulerManager::class.java)

    // TODO these are not that useful
    private val scheduledImportJobKey = JobKey.jobKey("scheduledCompleteImport", "imports")
    private val manualImportJobKey = JobKey.jobKey("manualCompleteImport", "imports")

    @PostConstruct
    fun initializeScheduler() {
        if (importSchedulerProperties.enabled) {
            schedulePeriodicImports(importSchedulerProperties.cron)
            logger.info("Scheduled fleet import with cron expression: {}", importSchedulerProperties.cron)
        } else {
            logger.info("Scheduled imports are disabled in configuration")
        }
    }

    private fun schedulePeriodicImports(cronExpression: String) {
        jobService.deschedulePendingOrScheluduledJobs()

        val newFleetJob =
            jobService.createJob(
                type = FleetJob.JobType.COMPLETE_IMPORT,
                manual = false,
                quartzJobId = scheduledImportJobKey.name,
                quartzJobGroup = scheduledImportJobKey.group,
            )

        MdcUtils.setJobId(newFleetJob.jobId)
        logger.info("Creating scheduled import job with ID {}", newFleetJob.jobId)

        val jobDetail =
            JobBuilder
                .newJob(CompleteImportJob::class.java)
                .withIdentity(scheduledImportJobKey)
                .usingJobData("jobId", newFleetJob.jobId)
                .usingJobData("jobType", "SCHEDULED")
                .build()

        val trigger =
            TriggerBuilder
                .newTrigger()
                .withIdentity("cronImportTrigger", "imports")
                .usingJobData("dummy", "dummy") // Work around Quartz deserialization bug // TODO check if still necessary
                .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                .build()

        try {
            scheduler.scheduleJob(jobDetail, trigger)
            logger.info("Successfully scheduled periodic import job with ID {}", newFleetJob.jobId)
        } catch (e: Exception) {
            logger.error("Error scheduling periodic fleet import", e)
            jobService.failJob(newFleetJob.jobId, "Failed to schedule: ${e.message}")
        }
    }

    fun startManualImport(): Boolean {
        val activeFleetJobs =
            jobService
                .findActiveJobs()
                .filter {
                    it.type == FleetJob.JobType.COMPLETE_IMPORT ||
                        it.type == FleetJob.JobType.IMPORT_FRIDA_VEHICLES ||
                        it.type == FleetJob.JobType.IMPORT_AUTOSYS
                }

        if (activeFleetJobs.isNotEmpty()) {
            logger.warn(
                "Attempt to start manual import while another import is in progress (ID {})",
                activeFleetJobs.first().jobId,
            )
            return false
        }

        val fleetJob =
            jobService.createJob(
                type = FleetJob.JobType.COMPLETE_IMPORT,
                manual = true,
                quartzJobId = manualImportJobKey.name,
                quartzJobGroup = manualImportJobKey.group,
            )

        MdcUtils.setJobId(fleetJob.jobId)
            logger.info("Starting manual import with ID {}", fleetJob.jobId)

            val jobDetail =
                JobBuilder
                    .newJob(CompleteImportJob::class.java)
                    .withIdentity(manualImportJobKey)
                    .usingJobData("jobId", fleetJob.jobId)
                    .usingJobData("jobType", "MANUAL")
                    .build()

            val trigger =
                TriggerBuilder
                    .newTrigger()
                    .withIdentity("manualImportTrigger", "imports")
                    .usingJobData("dummy", "dummy") // Work around Quartz deserialization bug
                    .startNow()
                    .build()

            return try {
                scheduler.scheduleJob(jobDetail, trigger)
                logger.info("Manual fleet import job scheduled successfully")
                true
            } catch (e: Exception) {
                logger.error("Error scheduling manual fleet import job", e)
                jobService.failJob(fleetJob.jobId, "Failed to schedule: ${e.message}")
                false
            }
    }

    fun scheduleJob(
        parentFleetJob: FleetJob? = null,
        fleetJobType: FleetJob.JobType,
        jobClass: Class<out org.quartz.Job>,
        parameters: Map<String, Any> = emptyMap(),
        startNow: Boolean = true,
    ): FleetJob {
        val job =
            if (parentFleetJob != null) {
                jobService.createChildJob(
                    parentFleetJob = parentFleetJob,
                    type = fleetJobType,
                    quartzJobId = generateJobKey(fleetJobType),
                    quartzJobGroup = fleetJobType.toString(),
                )
            } else {
                jobService.createJob(
                    type = fleetJobType,
                    manual = true,
                    quartzJobId = generateJobKey(fleetJobType),
                    quartzJobGroup = fleetJobType.toString(),
                )
            }

        val jobDataMap =
            JobDataMap().apply {
                put("jobId", job.jobId) // make sure jobId is always present
                put("jobType", fleetJobType.toString())
                if (parentFleetJob != null) {
                    put("parentJobId", parentFleetJob.jobId)
                }
                parameters.forEach { (key, value) -> put(key, value) }
            }

        val jobKey = JobKey.jobKey(job.quartzJobId, job.quartzJobGroup)
        val jobDetail =
            JobBuilder
                .newJob(jobClass)
                .withIdentity(jobKey)
                .usingJobData(jobDataMap)
                .build()

        val triggerId = "trigger-${job.quartzJobId}"
        val trigger =
            TriggerBuilder
                .newTrigger()
                .withIdentity(triggerId, job.quartzJobGroup)
                .usingJobData("dummy", "dummy") // work around Quartz deserialization bug
                .apply {
                    if (startNow) {
                        startNow()
                    }
                }.build()

        try {
            scheduler.scheduleJob(jobDetail, trigger)
            logger.info("Job scheduled successfully: {} with ID {}", fleetJobType, job.jobId)
            return job
        } catch (e: Exception) {
            logger.error("Error scheduling job: {}", e.message, e)
            jobService.failJob(job.jobId, "Failed to schedule: ${e.message}")
            throw e
        }
    }

    fun cancelJob(jobId: Int): Boolean {
        val job = jobService.findJob(jobId) ?: return false

        MdcUtils.setJobId(job.jobId)
        logger.info("Cancelling job with ID {}", job.jobId)
        jobService.cancelJob(job.jobId)

        // also try to cancel the Quartz job if it's still running
        try {
            if (job.quartzJobId != null && job.quartzJobGroup != null) {
                val jobKey = JobKey.jobKey(job.quartzJobId, job.quartzJobGroup)
                val context =
                    scheduler.currentlyExecutingJobs
                        .find { it.jobDetail.key == jobKey }

                if (context != null) {
                    context.result = "CANCELLED"
                    context.put("status", "CANCELLED")
                } else {
                    scheduler.deleteJob(jobKey)
                }
            }
        } catch (e: Exception) {
            logger.error("Error cancelling Quartz job for job {}", job.jobId, e)
        }
        return true
    }

    fun cancelActiveImport(): Boolean {
        val activeFleetJobs =
            jobService
                .findActiveJobs()
                .filter { it.type == FleetJob.JobType.COMPLETE_IMPORT || it.parentJobId == null }

        if (activeFleetJobs.isEmpty()) {
            logger.info("No active import to cancel")
            return false
        }

        var success = true
        activeFleetJobs.forEach { job ->
            success = success && cancelJob(job.jobId)
        }

        return success
    }

    fun isAnyImportInProgress(): Boolean {
        val activeFleetJobs =
            jobService
                .findActiveJobs()

        return activeFleetJobs.isNotEmpty()
    }

    fun getImportProgress(): Map<String, Any?> {
        val activeFleetJobs =
            jobService
                .findActiveJobs()
                .filter { it.type == FleetJob.JobType.COMPLETE_IMPORT }

        if (activeFleetJobs.isNotEmpty()) {
            MdcUtils.setJobId(activeFleetJobs.first().jobId)
            logger.debug("Getting progress for active import ID {}", activeFleetJobs.first().jobId)
            return jobService.getJobStatus(activeFleetJobs.first().jobId)
        }

        val recentFleetJob = jobService.findLatestJobByType(FleetJob.JobType.COMPLETE_IMPORT)
        if (recentFleetJob != null) {
            MdcUtils.setJobId(recentFleetJob.jobId)
            logger.debug("Getting status for recent import ID {}", recentFleetJob.jobId)
            val status = jobService.getJobStatus(recentFleetJob.jobId)
            return status + mapOf("inProgress" to false)
        }

        logger.debug("No import history found")
        return mapOf(
            "inProgress" to false,
            "message" to "No import in progress or history found.",
        )
    }

    private fun generateJobKey(fleetJobType: FleetJob.JobType): String =
        "${fleetJobType.toString().lowercase(Locale.getDefault())}-${UUID.randomUUID()}"
        
    fun startHistoricVehicleImport(): Boolean {
        val activeFleetJobs = jobService.findActiveJobs()
            .filter { it.type == FleetJob.JobType.IMPORT_FRIDA_VEHICLES }

        if (activeFleetJobs.isNotEmpty()) {
            logger.warn(
                "Attempt to start historic vehicle import while another import is in progress (ID {})",
                activeFleetJobs.first().jobId,
            )
            return false
        }

        val job = jobService.createJob(
            type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
            manual = true,
            quartzJobId = "historic-vehicle-import-${System.currentTimeMillis()}",
            quartzJobGroup = "imports",
        )

        val jobDetail = JobBuilder.newJob(FridaHistoricVehicleImportJob::class.java)
            .withIdentity("historicVehicleImportJob-${job.jobId}", "imports")
            .usingJobData("jobId", job.jobId)
            .build()

        val trigger = TriggerBuilder.newTrigger()
            .withIdentity("historicVehicleImportTrigger-${job.jobId}", "imports")
            .usingJobData("dummy", "dummy") // Work around Quartz deserialization bug
            .startNow()
            .build()

        return try {
            scheduler.scheduleJob(jobDetail, trigger)
            logger.info("Historic vehicle import job scheduled successfully with ID {}", job.jobId)
            true
        } catch (e: Exception) {
            logger.error("Error scheduling historic vehicle import job", e)
            jobService.failJob(job.jobId, "Failed to schedule: ${e.message}")
            false
        }
    }
}
