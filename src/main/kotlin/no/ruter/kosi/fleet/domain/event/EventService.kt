package no.ruter.kosi.fleet.domain.event

import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.scd2.ISCD2Service
import no.ruter.kosi.fleet.domain.scd2.SCD2Service
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Service
class EventService(
    private val jobRepository: AuroraJobRepository,
    private val eventDataRepository: AuroraEventDataRepository,
    private val eventRepository: AuroraEventRepository,
    private val eventDataSCD2Service: ISCD2Service<EventData, Int, String>,
) {
    private val logger = LoggerFactory.getLogger(EventService::class.java)

    @Transactional
    fun storeChangedFieldsFromVehicle(
        vehicle: InternalVehicle,
        extractedFields: Map<String, Any?>,
        dataSource: DataSource,
        job: FleetJob,
    ): InternalVehicle {
        val changedFields = extractChangedFields(vehicle, extractedFields)
        if (changedFields.isNotEmpty()) {
            logger.info("Found {} changed fields for vehicle", changedFields.size)
            saveEvent(vehicle, changedFields, dataSource, job)
        } else {
            logger.debug("No changes detected for vehicle")
        }
        return vehicle
    }

    private fun saveEvent(
        vehicle: InternalVehicle,
        changedFields: Map<String, Any?>,
        dataSource: DataSource,
        job: FleetJob,
    ) {
        val vehiclesWithFields =
            listOf(
                vehicle to changedFields,
            )

        val managedJob =
            if (job.jobId > 0) {
                jobRepository.findById(job.jobId) ?: throw IllegalStateException("Job ${job.jobId} not found")
            } else {
                jobRepository.save(job)
            }

        batchStoreChangedFields(
            vehiclesWithFields = vehiclesWithFields,
            dataSource = dataSource,
            job = managedJob,
        )
    }

    @Transactional
    fun batchStoreChangedFields(
        vehiclesWithFields: List<Pair<InternalVehicle, Map<String, Any?>>>,
        dataSource: DataSource,
        job: FleetJob,
    ): List<Pair<Event, InternalVehicle>> {
        logger.debug("Batch storing changed fields for {} vehicles", vehiclesWithFields.size)

        val managedJob =
            if (job.jobId > 0) {
                jobRepository.findById(job.jobId) ?: throw IllegalStateException("Job ${job.jobId} not found")
            } else {
                jobRepository.save(job)
            }

        val events =
            vehiclesWithFields.map { (vehicle, _) ->
                Event.Companion.create(
                    vehicle = vehicle,
                    source = dataSource.toString(),
                    eventDataList = emptyList(),
                    priority = 1,
                    job = managedJob,
                )
            }

        val savedEvents = eventRepository.saveAll(events)

        val allEventData = mutableListOf<EventData>()

        savedEvents.forEachIndexed { index, event ->
            val (_, fields) = vehiclesWithFields[index]

            val now = Instant.now()
            fields.forEach { (fieldName, newValue) ->
                val eventData =
                    EventData(
                        event = event,
                        fieldName = fieldName,
                        fieldValue = newValue?.toString(),
                        timestamp = now,
                        businessKey = "${event.vehicle.vehicleRef!!}-$fieldName",
                    )
                allEventData.add(eventData)
            }
        }

        if (allEventData.isNotEmpty()) {
            eventDataSCD2Service.saveNewVersions(allEventData)
            logger.info(
                "Stored {} new EventData entries across {} vehicles",
                allEventData.size,
                vehiclesWithFields.size,
            )
        } else {
            logger.debug("No field changes to store for any vehicles")
        }

        return savedEvents.zip(vehiclesWithFields.map { it.first })
    }

    fun extractChangedFields(
        vehicle: InternalVehicle,
        extractedFields: Map<String, Any?>,
    ): Map<String, Any?> {
        val currentFields = eventDataRepository.findByVehicleId(vehicle.vehicleId).associateBy { it.fieldName }

        val changedFields =
            extractedFields.filter { (fieldName, newValue) ->
                val existingField = currentFields[fieldName]
                val isChanged = existingField == null || existingField.fieldValue.toString() != newValue.toString()

                if (isChanged && logger.isTraceEnabled) {
                    logger.trace(
                        "Field '{}' changed from '{}' to '{}'",
                        fieldName,
                        existingField?.fieldValue,
                        newValue,
                    )
                }

                isChanged
            }

        if (logger.isDebugEnabled && changedFields.isNotEmpty()) {
            logger.debug(
                "Found {} changed fields for vehicle: {}",
                changedFields.size,
                changedFields.keys.joinToString(", "),
            )
        }

        return changedFields
    }

    @Transactional
    fun createEventWithData(
        vehicle: InternalVehicle,
        fields: Map<String, Any?>,
        dataSource: DataSource,
        jobId: Int,
    ): Event {
        val job =
            jobRepository.findById(jobId)
                ?: throw IllegalStateException("Import not found")

        logger.debug(
            "Creating event with {} fields for vehicle {}",
            fields.size,
            vehicle.vehicleId,
        )

        val event =
            Event.Companion.create(
                vehicle = vehicle,
                source = dataSource.toString(),
                eventDataList = emptyList(),
                priority = 1,
                job = job,
            )

        val savedEvent = eventRepository.save(event)

        val now = Instant.now()
        val eventDataList = mutableListOf<EventData>()

        fields.forEach { (fieldName, value) ->
            val eventData =
                EventData(
                    event = savedEvent,
                    fieldName = fieldName,
                    fieldValue = value?.toString(),
                    timestamp = now,
                    businessKey = "${savedEvent.vehicle.vehicleRef!!}-$fieldName",
                )

            val savedEventData = eventDataSCD2Service.saveNewVersion(eventData, now)

            eventDataList.add(savedEventData)
        }

        savedEvent.eventDataList.addAll(eventDataList)

        logger.debug(
            "Created event {} with {} data fields for vehicle {}",
            savedEvent.eventId,
            eventDataList.size,
            vehicle.vehicleId,
        )

        return savedEvent
    }
}