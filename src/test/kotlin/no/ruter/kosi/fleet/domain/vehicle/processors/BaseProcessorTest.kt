package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.getErrorMessage
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import java.time.Instant
import kotlin.reflect.KClass
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
abstract class BaseProcessorTest<T : VehicleFieldProcessor> {

    protected var mockEvent = mockk<Event>(relaxed = true)

    protected abstract val processor: T

    protected open fun createEventData(value: String, fieldName: String = "testField"): EventData {
        return EventData(
            event = mockEvent,
            fieldName = fieldName,
            fieldValue = value,
            timestamp = Instant.now(),
            businessKey = "$fieldName-${mockEvent.vehicle.vehicleRef!!}"
        )
    }

    protected fun assertProcessSuccess(
        eventData: Map<String, EventData?> = emptyMap(),
        expectedValue: Any?,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField> = emptyMap(),
        lastSnapshot: VehicleEntity? = null,
        timestamp: Instant? = null
    ) {
        val result = processor.process(
            eventData,
            processorResults,
            lastSnapshot,
            timestamp
        )

        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()?.getErrorMessage()}")
        val processedField = (result as Either.Right<ProcessedField>).value
        assertEquals(expectedValue, processedField.value)
    }

    protected fun assertProcessFailure(
        eventData: Map<String, EventData?> = emptyMap(),
        expectedErrorType: KClass<out ProcessingError>,
        expectedMessage: String? = null,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField> = emptyMap(),
        lastSnapshot: VehicleEntity? = null,
        timestamp: Instant? = null
    ) {
        val result = processor.process(
            eventData,
            processorResults,
            lastSnapshot,
            timestamp
        )

        assertTrue(result.isLeft(), "Expected failure but got success")
        val error = (result as Either.Left<ProcessingError>).value
        assertTrue(
            expectedErrorType.isInstance(error),
            "Expected ${expectedErrorType.simpleName} but got ${error::class.simpleName}"
        )
        expectedMessage?.let { message ->
            assertEquals(message, error.getErrorMessage())
        }
    }

    protected fun createQuality(value: Any) = VehicleFieldQuality(
        value = value,
        type = QualityType.VALID,
        message = "Test quality"
    )

    protected fun createProcessedField(
        processor: KClass<out VehicleFieldProcessor>,
        value: Any?,
        quality: VehicleFieldQuality = createQuality(value ?: "")
    ) = ProcessedField(
        originProcessor = processor,
        value = value,
        quality = quality
    )
    
    data class ProcessorTestCase<T : Any>(
        val name: String,
        val input: Map<String, String?> = emptyMap(),
        val expectedResult: Either<ProcessingError, ProcessedField>? = null,
        val expectedValue: T? = null,
        val expectedError: ProcessingError? = null,
        val processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField> = emptyMap(),
        val lastSnapshot: VehicleEntity? = null,
        val timestamp: Instant? = null
    ) {
        init {
            require(expectedResult != null || expectedValue != null || expectedError != null) {
                "Either expectedResult, expectedValue, or expectedError must be provided"
            }
        }
    }
    
    protected fun <T : Any> runTestCases(testCases: List<ProcessorTestCase<T>>) {
        testCases.forEach { testCase ->
            val inputEventData = testCase.input.mapValues { (fieldName, value) ->
                value?.let { createEventData(it, fieldName) }
            }

            val result = processor.process(
                inputEventData,
                testCase.processorResults,
                testCase.lastSnapshot,
                testCase.timestamp
            )

            when {
                testCase.expectedResult != null -> {
                    assertEquals(testCase.expectedResult, result, "Test case '${testCase.name}' failed")
                }
                testCase.expectedValue != null -> {
                    assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()?.getErrorMessage()}")
                    val processedField = (result as Either.Right<ProcessedField>).value
                    assertEquals(testCase.expectedValue, processedField.value, "Test case '${testCase.name}' failed")
                }
                testCase.expectedError != null -> {
                    assertTrue(result.isLeft(), "Expected failure but got success for test case '${testCase.name}'")
                    val error = (result as Either.Left<ProcessingError>).value
                    assertTrue(
                        testCase.expectedError::class.isInstance(error),
                        "Expected ${testCase.expectedError::class.simpleName} but got ${error::class.simpleName} in test case '${testCase.name}'"
                    )
                    assertEquals(
                        testCase.expectedError.getErrorMessage(),
                        error.getErrorMessage(),
                        "Test case '${testCase.name}' failed: Error messages don't match"
                    )
                }
            }
        }
    }
}
