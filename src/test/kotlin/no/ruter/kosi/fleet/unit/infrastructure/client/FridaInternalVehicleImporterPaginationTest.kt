package no.ruter.kosi.fleet.unit.infrastructure.client

import arrow.core.getOrElse
import io.mockk.every
import io.mockk.mockk
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaVehicleImporter
import no.ruter.kosi.fridaclient.client.ApiContractContractsClient
import no.ruter.kosi.fridaclient.client.ApiResponse
import no.ruter.kosi.fridaclient.client.ApiVehicleVehiclesClient
import no.ruter.kosi.fridaclient.models.ApiListResponseModelOfApiVehicleV2Model
import no.ruter.kosi.fridaclient.models.ApiVehicleV2Model
import okhttp3.Headers
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FridaInternalVehicleImporterPaginationTest {
    private val vehiclesClient: ApiVehicleVehiclesClient = mockk()
    private val contractorsClient: ApiContractContractsClient = mockk()
    private lateinit var fridaVehicleImporter: FridaVehicleImporter
    private lateinit var fridaApiService: FridaApiService

    @BeforeEach
    fun setup() {
        fridaVehicleImporter =
            FridaVehicleImporter(
                eventService = mockk(),
                objectMapper = mockk(),
                vehicleRepository = mockk(),
            )
        fridaApiService = FridaApiService(
            vehiclesClient = vehiclesClient,
            contractorsClient = contractorsClient,
        )
    }

    @Test
    fun `fetchVehiclesFromApi - should fetch all pages and return all vehicles`() {
        val pageSize = 200
        val totalVehiclesCount = 455

        // mock responses for each page
        val vehiclesPage1 = List(pageSize) { ApiVehicleV2Model(id = it, customAttributeValues = null) }
        val vehiclesPage2 = List(pageSize) { ApiVehicleV2Model(id = it + pageSize, customAttributeValues = null) }
        val vehiclesPage3 =
            List(totalVehiclesCount % pageSize) {
                ApiVehicleV2Model(id = it + 2 * pageSize, customAttributeValues = null)
            }

        every { vehiclesClient.vehiclesVGetAllVehicles() } returns
            ApiResponse(
                headers = Headers.headersOf("mockheader", "value"),
                data =
                    ApiListResponseModelOfApiVehicleV2Model(
                        items = vehiclesPage1,
                        totalCount = totalVehiclesCount,
                    ),
                statusCode = 200,
            )

        every { vehiclesClient.vehiclesVGetAllVehicles(page = 1, pageSize = pageSize) } returns
            ApiResponse(
                headers = Headers.headersOf("mockheader", "value"),
                data =
                    ApiListResponseModelOfApiVehicleV2Model(
                        items = vehiclesPage2,
                        totalCount = totalVehiclesCount,
                    ),
                statusCode = 200,
            )

        every { vehiclesClient.vehiclesVGetAllVehicles(page = 2, pageSize = pageSize) } returns
            ApiResponse(
                headers = Headers.headersOf("mockheader", "value"),
                data =
                    ApiListResponseModelOfApiVehicleV2Model(
                        items = vehiclesPage3,
                        totalCount = totalVehiclesCount,
                    ),
                statusCode = 200,
            )

        val vehicles = fridaApiService.fetchVehicles().getOrElse { throw IllegalStateException("test error") }

        assertEquals(totalVehiclesCount, vehicles.size)
        assertEquals((0 until totalVehiclesCount).toList(), vehicles.map { it.id })
    }
}
